import { Router } from 'express';
import { body } from 'express-validator';
import { AuthController } from './auth.controller';
import { authMiddleware } from '../middleware/auth';
import { validationErrorHandler } from '../middleware/validationErrorHandler';

const router = Router();
const authController = new AuthController();

// Validation rules
const registerValidation = [
  body('email').isEmail().withMessage('Email must be valid'),
  body('username').isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('fullName').notEmpty().withMessage('Full name is required'),
  validationErrorHandler
];

const loginValidation = [
  body('email').isEmail().withMessage('Email must be valid'),
  body('password').notEmpty().withMessage('Password is required'),
  validationErrorHandler
];

const refreshTokenValidation = [
  body('refreshToken').notEmpty().withMessage('Refresh token is required'),
  validationErrorHandler
];

// Routes
router.get('/me', 
  authMiddleware,
  authController.me.bind(authController)
);

router.post('/register',
  registerValidation,
  authController.register.bind(authController)
);

router.post('/login',
  loginValidation,
  authController.login.bind(authController)
);

router.post('/refresh-token',
  refreshTokenValidation,
  authController.refreshToken.bind(authController)
);

router.post('/logout',
  authController.logout.bind(authController)
);

router.get('/profile',
  authMiddleware,
  authController.getProfile.bind(authController)
);

export default router;
