import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

export enum UserStatus {
    ACTIVE = 'ACTIVE',
    BANNED = 'BANNED',
}

export enum UserRole {
    USER = 'USER',
    ADMIN = 'ADMIN',
}

@Entity()
export class User {
    @PrimaryGeneratedColumn()
    id: string;

    @Column({
        nullable: true,
        default: '/uploads/images/1751899622745-834467477.jpg',
    })
    avatar: string;

    @Column({
        unique: true,
        nullable: false,
    })
    username: string;

    @Column({
        nullable: false,
    })
    password: string;

    @Column({
        unique: true,
        nullable: false,
    })
    email: string;

    @Column({
        nullable: true,
        default: UserStatus.ACTIVE,
    })
    status: UserStatus;

    @Column({
        nullable: true,
        default: UserRole.USER,
    })
    role: UserRole;

    @Column({
        nullable: true,
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @Column({
        nullable: true,
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;
    
    @Column({
        default: false,
    })
    IsDeleted: boolean;
}
