import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { CriminalRecordsDto, createCriminalRecordDto, UpdateCriminalRecordDto } from './dto/criminial_records.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CriminalRecord, status } from './entities/criminal_record.entity';
import { People } from 'src/people/entities/people.entity';
import { PaginationDto } from 'src/people/dto/pagination.dto';
import { DEFAULT_PAGE_SIZE } from 'src/ultils/constants';
import { LoggerService } from 'src/logger/logger.service';

@Injectable()
export class CriminalRecordsService {
  constructor(
    @InjectRepository(CriminalRecord)
    private readonly criminalRecordRepository: Repository<CriminalRecord>,
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    private readonly loggerService: LoggerService
  ) {}

  async create(dto: CriminalRecordsDto) {
    // Kiểm tra xem peopleId có tồn tại không
    if (!dto.peopleId) {
      throw new BadRequestException('peopleId is required');
    }

    // Tìm người dựa trên peopleId
    const people = await this.peopleRepository.findOne({
      where: { CCCD: dto.peopleId }
    });

    if (!people) {
      throw new NotFoundException(`Person with CCCD ${dto.peopleId} not found`);
    }

    // Tạo đối tượng mới từ DTO
    const newCriminalRecord = new CriminalRecord();
    newCriminalRecord.offense = dto.offense;
    newCriminalRecord.date = dto.date;
    newCriminalRecord.location = dto.location;
    newCriminalRecord.sentence = dto.sentence || '';
    newCriminalRecord.status = dto.status || status.PROGRESS;
    newCriminalRecord.people = people;
    
    // Lưu vào database
    await this.criminalRecordRepository.save(newCriminalRecord);

    this.loggerService.info(`New criminal record created: ${newCriminalRecord.offense} (CCCD: ${newCriminalRecord.people.CCCD})`);

    return { 
      message: 'Criminal record created successfully',
      data: newCriminalRecord
    };
  }

  async findAll(paginationDto: PaginationDto) {
      // this.loggerService.info('Retrieved all people records');
      const [data, total] = await this.criminalRecordRepository.findAndCount({
        skip: paginationDto.skip,
        take: paginationDto.limit ?? DEFAULT_PAGE_SIZE,
        where: { IsDeleted: false },
        relations: ['people', 'evidenceDetails']
      });
  
      return {
        message: 'Criminal records retrieved successfully',
        total,
        data
      }
    }

  async findOne(id: string) {
    const record = await this.criminalRecordRepository.findOne({
      where: { id: id.toString() },
      relations: ['people', 'evidenceDetails']
    });

    if (!record) {
      throw new NotFoundException(`Criminal record with ID ${id} not found`);
    }

    return record;
  }

  async update(id: number, updateCriminalRecordDto: UpdateCriminalRecordDto) {
    const criminalRecord = await this.criminalRecordRepository.findOne({
      where: { id: id.toString() },
      relations: ['people']
    });
    
    if (!criminalRecord) {
      throw new NotFoundException(`Criminal record with ID ${id} not found`);
    }
    
    // Cập nhật thông tin
    criminalRecord.offense = updateCriminalRecordDto.offense;
    criminalRecord.date = updateCriminalRecordDto.date;
    criminalRecord.location = updateCriminalRecordDto.location;
    criminalRecord.sentence = updateCriminalRecordDto.sentence || criminalRecord.sentence;
    criminalRecord.status = updateCriminalRecordDto.status || criminalRecord.status;
    criminalRecord.updatedAt = new Date();
    
    await this.criminalRecordRepository.save(criminalRecord);

    this.loggerService.info(`Criminal record updated: ${criminalRecord.offense} (CCCD: ${criminalRecord.people.CCCD})`);
    
    return { 
      message: 'Criminal record updated successfully',
      data: criminalRecord
    };
  }

  async remove(id: string) {
    const criminalRecord = await this.criminalRecordRepository.findOne({
      where: { id: id }
    });
    
    if (!criminalRecord) {
      throw new NotFoundException(`Criminal record with ID ${id} not found`);
    }
    
    criminalRecord.IsDeleted = true;
    criminalRecord.updatedAt = new Date();

    await this.criminalRecordRepository.save(criminalRecord);

    this.loggerService.info(`Criminal record deleted: ${criminalRecord.offense} (CCCD: ${criminalRecord.people.CCCD})`);
    
    return { message: 'Criminal record deleted successfully' };
  }

  async searchQuery(searchTerm: string, paginationDto: PaginationDto) {
    // Sử dụng QueryBuilder để có nhiều kiểm soát hơn
    const queryBuilder = this.criminalRecordRepository.createQueryBuilder('criminalRecord');
    
    // Thêm các relations để lấy thông tin liên quan
    queryBuilder.leftJoinAndSelect('criminalRecord.people', 'people')
               .leftJoinAndSelect('criminalRecord.evidenceDetails', 'evidenceDetails');

    // Thêm điều kiện tìm kiếm với OR
    queryBuilder.where('criminalRecord.IsDeleted = false')
      .andWhere(
        '(criminalRecord.offense LIKE :offense OR ' +
        'criminalRecord.location LIKE :location OR ' +
        'people.CCCD LIKE :CCCD)',
        {
          offense: `%${searchTerm}%`,
          location: `%${searchTerm}%`,
          CCCD: `%${searchTerm}%`
        }
      );

    // Thêm phân trang
    queryBuilder.skip(paginationDto.skip)
      .take(paginationDto.limit ?? DEFAULT_PAGE_SIZE);

    // Thực hiện truy vấn
    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      message: 'Criminal records retrieved successfully',
      total,
      data
    };
  }
}
