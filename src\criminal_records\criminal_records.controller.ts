import { Controller, Get, Post, Body, Patch, Param, Delete, Put, Query } from '@nestjs/common';
import { CriminalRecordsService } from './criminal_records.service';
import { CriminalRecordsDto, UpdateCriminalRecordDto } from './dto/criminial_records.dto';
import { PaginationDto } from 'src/people/dto/pagination.dto';

@Controller('criminal-records')
export class CriminalRecordsController {
  constructor(private readonly criminalRecordsService: CriminalRecordsService) {}

  @Post("create-criminal-record")
  create(@Body() createCriminalRecordDto: CriminalRecordsDto) {
    return this.criminalRecordsService.create(createCriminalRecordDto);
  }

  @Get('get-all-criminal-records')
  findAll(@Query() paginationDto: PaginationDto) {
    return this.criminalRecordsService.findAll({
      skip: paginationDto.skip,
      limit: paginationDto.limit
    });
  }

  // @Get('criminial-record-detail/:id')
  // findOne(@Param('id') id: string) {
  //   return this.criminalRecordsService.findOne(id);
  // }

  @Put('update-criminal-record/:id')
  update(@Param('id') id: string, @Body() updateCriminalRecordDto: UpdateCriminalRecordDto) {
    return this.criminalRecordsService.update(+id, updateCriminalRecordDto);
  }

  @Put('delete-criminial-records/:id')
  remove(@Param('id') id: string) {
    return this.criminalRecordsService.remove(id);
  }

  @Get('search-criminal-records')
  search(@Query('searchTerm') searchTerm: string, @Query() paginationDto: PaginationDto) {
    return this.criminalRecordsService.searchQuery(searchTerm, {
      skip: paginationDto.skip,
      limit: paginationDto.limit
    });
  }
}
