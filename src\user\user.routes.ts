import { Router } from 'express';
import { body, query, param } from 'express-validator';
import { UserController } from './user.controller';
import { authMiddleware, roleMiddleware } from '../middleware/auth';
import { UserRole } from './entities/user.entity';
import { validationErrorHandler } from '../middleware/validationErrorHandler';

const router = Router();
const userController = new UserController();

// Validation rules
const createUserValidation = [
  body('email').isEmail().withMessage('Email must be valid'),
  body('username').isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('fullName').notEmpty().withMessage('Full name is required'),
  validationErrorHandler
];

const updateUserValidation = [
  param('id').isNumeric().withMessage('ID must be a number'),
  body('email').optional().isEmail().withMessage('Email must be valid'),
  body('username').optional().isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
  body('fullName').optional().notEmpty().withMessage('Full name cannot be empty'),
  validationErrorHandler
];

const paginationValidation = [
  query('skip').optional().isNumeric().withMessage('Skip must be a number'),
  query('limit').optional().isNumeric().withMessage('Limit must be a number'),
  validationErrorHandler
];

const searchValidation = [
  query('searchTerm').notEmpty().withMessage('Search term is required'),
  ...paginationValidation
];

// Routes
router.get('/get-all-user', 
  authMiddleware, 
  roleMiddleware(UserRole.ADMIN, UserRole.USER),
  paginationValidation,
  userController.findAll.bind(userController)
);

router.post('/create',
  authMiddleware,
  roleMiddleware(UserRole.ADMIN, UserRole.USER),
  createUserValidation,
  userController.create.bind(userController)
);

router.put('/update-user/:id',
  authMiddleware,
  roleMiddleware(UserRole.ADMIN),
  updateUserValidation,
  userController.update.bind(userController)
);

router.delete('/delete-user/:id',
  authMiddleware,
  roleMiddleware(UserRole.ADMIN),
  param('id').isNumeric().withMessage('ID must be a number'),
  validationErrorHandler,
  userController.remove.bind(userController)
);

router.get('/search-user',
  authMiddleware,
  roleMiddleware(UserRole.ADMIN, UserRole.USER),
  searchValidation,
  userController.search.bind(userController)
);

export default router;
