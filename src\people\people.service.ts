import { CreatePeopleDto, UpdatePeopleDto } from './dto/people.dto';
import { Repository } from 'typeorm';
import { People } from './entities/people.entity';
import { PaginationDto } from './dto/pagination.dto';
import { DEFAULT_PAGE_SIZE } from '../ultils/constants';
import { AppDataSource } from '../config/database';

export class PeopleService {
  private peopleRepository: Repository<People>;

  constructor() {
    this.peopleRepository = AppDataSource.getRepository(People);
  }

  async create(createPersonDto: CreatePeopleDto, userId: string) {
    const found = await this.peopleRepository.findOne({
      where: { CCCD: createPersonDto.CCCD }
    });

    if (found) {
      console.warn(`Attempt to create person with existing CCCD: ${createPersonDto.CCCD} by USER_ID ${userId}`);
      throw new Error('CCCD already exists');
    }

    if(!createPersonDto.avatar){
      createPersonDto.avatar = '/uploads/images/1751899622745-834467477.jpg';
    }

    const newPeople = this.peopleRepository.create(createPersonDto);

    await this.peopleRepository.save(newPeople);

    // Log the action
    console.log(`New person created: ${newPeople.fullname} (CCCD: ${newPeople.CCCD}) by USER_ID ${userId}`);

    return { message: 'Person created successfully', person: newPeople };
  }

  async findAll(paginationDto: PaginationDto) {
    // this.loggerService.info('Retrieved all people records');
    const [data, total] = await this.peopleRepository.findAndCount({
      skip: paginationDto.skip,
      take: paginationDto.limit ?? DEFAULT_PAGE_SIZE,
      where: { IsDeleted: false }
    });

    return {
      message: 'People retrieved successfully',
      total,
      data
    }
  }

  async findOne(cccd: string) {
    console.log(`Retrieved person with CCCD: ${cccd}`);
    const person = await this.peopleRepository.findOne({
      where: { CCCD: cccd, IsDeleted: false }
    });

    if (!person) {
      throw new Error('Person not found');
    }

    return { message: 'Person retrieved successfully', person };
  }

  async filter(type: string, result: string) {
    console.log(`Filtering people by ${type}: ${result}`);

    // Kiểm tra loại tìm kiếm và trả về kết quả tương ứng
    if (type === 'CCCD') {
      return this.peopleRepository.findOne({
        where: { CCCD: result, IsDeleted: false }
      });
    }

    if (type === 'fullname') {
      return this.peopleRepository.find({
        where: { fullname: result, IsDeleted: false }
      });
    }

    throw new Error('Invalid filter type');
  }

  async update(id: string, updatePersonDto: UpdatePeopleDto) {
    const found = await this.peopleRepository.findOne({
      where: { CCCD: id, IsDeleted: false }
    });

    if (!found) {
      console.warn(`Attempt to update non-existent person with CCCD: ${id}`);
      throw new Error('Person not found');
    }

    // Set time when updated
    updatePersonDto.updatedAt = new Date();
    Object.assign(found, updatePersonDto);

    await this.peopleRepository.save(found);

    console.log(`Person updated: ${found.fullname} (CCCD: ${found.CCCD})`);

    return { message: 'Person updated successfully', person: found };
  }
  
  async remove(id: string) {
    const found = await this.peopleRepository.findOne({
      where: { CCCD: id, IsDeleted: false }
    });

    if (!found) {
      console.warn(`Attempt to delete non-existent person with CCCD: ${id}`);
      throw new Error('Person not found');
    }

    // Soft delete
    found.IsDeleted = true;
    await this.peopleRepository.save(found);

    console.log(`Person deleted: ${found.fullname} (CCCD: ${found.CCCD})`);

    return { message: 'Person deleted successfully' };
  }


  async searchQuery(searchTerm: string, paginationDto: PaginationDto) {
    // Sử dụng QueryBuilder để có nhiều kiểm soát hơn
    const queryBuilder = this.peopleRepository.createQueryBuilder('people');
    
    // Thêm điều kiện tìm kiếm với OR
    queryBuilder.where('people.IsDeleted = false')
      .andWhere(
        '(people.CCCD LIKE :cccd OR ' +
        'people.fullname LIKE :fullname OR ' +
        'people.phone LIKE :phone OR ' +
        'people.address LIKE :address)',
        {
          cccd: `%${searchTerm}%`,
          fullname: `%${searchTerm}%`,
          phone: `%${searchTerm}%`,
          address: `%${searchTerm}%`
        }
      );
    
    // Thêm phân trang
    queryBuilder.skip(paginationDto.skip)
      .take(paginationDto.limit ?? DEFAULT_PAGE_SIZE);
    
    // Thực hiện truy vấn
    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      message: 'People retrieved successfully',
      total,
      data
    };
  }
  
}
