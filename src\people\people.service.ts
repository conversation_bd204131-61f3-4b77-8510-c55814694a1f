import { Injectable } from '@nestjs/common';
import { CreatePeopleDto, UpdatePeopleDto } from './dto/people.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Repository } from 'typeorm';
import { People } from './entities/people.entity';
import { SocketGateway } from 'src/socket/socket.gateway';
import { LoggerService } from 'src/logger/logger.service';
import { PaginationDto } from './dto/pagination.dto';
import { DEFAULT_PAGE_SIZE } from 'src/ultils/constants';
import { UploadService } from 'src/upload/upload.service';


@Injectable()
export class PeopleService {
  constructor(
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    private readonly socketGateway: SocketGateway,
    private readonly loggerService: LoggerService,
    private readonly uploadService: UploadService
  ) {}  

  async create(createPersonDto: CreatePeopleDto, userId: string) {
    const found = await this.peopleRepository.findOne({
      where: { CCCD: createPersonDto.CCCD }
    });

    if (found) {
      this.loggerService.warning(`Attempt to create person with existing CCCD: ${createPersonDto.CCCD} by USER_ID ${userId}`);
      return { message: 'CCCD already exists' };
    }
    
    if(!createPersonDto.avatar){
      createPersonDto.avatar = '/uploads/images/1751899622745-834467477.jpg';
    }

    const newPeople = this.peopleRepository.create(createPersonDto);
    
    await this.peopleRepository.save(newPeople);

    // websocket 
    this.socketGateway.handlePersonCreated(newPeople);
    
    // Log the action
    this.loggerService.info(`New person created: ${newPeople.fullname} (CCCD: ${newPeople.CCCD}) by USER_ID ${userId}`);

    return { message: 'Person created successfully' };
  }

  async findAll(paginationDto: PaginationDto) {
    // this.loggerService.info('Retrieved all people records');
    const [data, total] = await this.peopleRepository.findAndCount({
      skip: paginationDto.skip,
      take: paginationDto.limit ?? DEFAULT_PAGE_SIZE,
      where: { IsDeleted: false }
    });

    return {
      message: 'People retrieved successfully',
      total,
      data
    }
  }

  findOne(id: number) {
    this.loggerService.info(`Retrieved person with ID: ${id}`);
    return `This action returns a #${id} person`;
  }

  filter(type: string, result: string) {
    this.loggerService.info(`Filtering people by ${type}: ${result}`);
    
    // Kiểm tra loại tìm kiếm và trả về kết quả tương ứng
    if (type === 'CCCD') {
      return this.peopleRepository.findOne({
        where: { CCCD: result }
      });
    } 

    if (type === 'fullname') {
      return this.peopleRepository.find({
        where: { fullname: result }
      });
    } 
      
    return { message: 'Person not found' };
  }

  async update(id: string, updatePersonDto: UpdatePeopleDto) {
    const found = await this.peopleRepository.findOne({
      where: { CCCD: id }
    });

    if (!found) {
      this.loggerService.warning(`Attempt to update non-existent person with CCCD: ${id}`);
      return { message: 'Person not found' };
    }

    // Set time when updated
    updatePersonDto.updatedAt = new Date();
    Object.assign(found, updatePersonDto);
    
    await this.peopleRepository.save(found);
    
    this.loggerService.info(`Person updated: ${found.fullname} (CCCD: ${found.CCCD})`);

    return { message: 'Person updated successfully' };
  }
  
  async remove(id: string) {
    const found = await this.peopleRepository.findOne({
      where: { CCCD: id }
    });
    
    if (!found) {
      this.loggerService.warning(`Attempt to delete non-existent person with CCCD: ${id}`);
      return { message: 'Person not found' };
    }
    
    // Soft delete
    found.IsDeleted = true;
    await this.peopleRepository.save(found);
    
    this.loggerService.info(`Person deleted: ${found.fullname} (CCCD: ${found.CCCD})`);
    
    return { message: 'Person deleted successfully' };
  }


  async searchQuery(searchTerm: string, paginationDto: PaginationDto) {
    // Sử dụng QueryBuilder để có nhiều kiểm soát hơn
    const queryBuilder = this.peopleRepository.createQueryBuilder('people');
    
    // Thêm điều kiện tìm kiếm với OR
    queryBuilder.where('people.IsDeleted = false')
      .andWhere(
        '(people.CCCD LIKE :cccd OR ' +
        'people.fullname LIKE :fullname OR ' +
        'people.phone LIKE :phone OR ' +
        'people.address LIKE :address)',
        {
          cccd: `%${searchTerm}%`,
          fullname: `%${searchTerm}%`,
          phone: `%${searchTerm}%`,
          address: `%${searchTerm}%`
        }
      );
    
    // Thêm phân trang
    queryBuilder.skip(paginationDto.skip)
      .take(paginationDto.limit ?? DEFAULT_PAGE_SIZE);
    
    // Thực hiện truy vấn
    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      message: 'People retrieved successfully',
      total,
      data
    };
  }
  
}
