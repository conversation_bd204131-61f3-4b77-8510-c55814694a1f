import { Injectable, UnauthorizedException, HttpException, HttpStatus } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from 'src/user/user.service';
import { SignUpDto, SignInDto } from 'src/user/dto/user.dto';
import { UserRole } from 'src/user/entities/user.entity';

// Định nghĩa interface cho kiểu trả về
export interface LoginResponse {
  message: string;
  accessToken?: string;
  refreshToken?: string;
  user?: {
    id: string;
    username: string;
    email: string;
    role: UserRole;
  };
}

export interface RefreshTokenResponse {
  accessToken: string;
}

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService
  ) {}

  async register(registerDto: SignUpDto): Promise<HttpException | { message: string }> {
    return this.userService.create(registerDto);
  }

  async login(loginDto: SignInDto, response: any): Promise<LoginResponse> {
    // Tìm user theo email
    const user = await this.userService.findByEmail(loginDto.email);
    
    if (!user) {
      return { message: "Email không tồn tại!" };
    }
    
    // Kiểm tra mật khẩu
    if (user.password !== loginDto.password) {
      return { message: "Mật khẩu không chính xác!" };
    }

    // Tạo payload cho token
    const payload = {
      _id: user.id,
      role: user.role
    };
    
    // Tạo access token (thời gian ngắn)
    const accessToken = this.jwtService.sign(payload, {
      secret: process.env.JWT_SECRET,
      expiresIn: process.env.JWT_EXPIRATION_TIME,
    });

    // Tạo refresh token (thời gian dài)
    const refreshToken = this.jwtService.sign(payload, {
      secret: process.env.JWT_REFRESH_SECRET,
      expiresIn: process.env.JWT_REFRESH_EXPIRATION_TIME,
    });
    
    // Lưu token vào cookie
    if (response && response.cookie) {
      response.cookie('jwt', accessToken, {
        httpOnly: true,
        maxAge: 15 * 60 * 1000, // 15 phút
      });
      
      response.cookie('refresh_token', refreshToken, {
        httpOnly: true,
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 ngày
      });
    }
    
    return {
      message: 'Đăng nhập thành công',
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      }
    };
  }

  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    
    try {
      // Xác thực refresh token
      const payload = await this.jwtService.verifyAsync(refreshToken, {
        secret: process.env.JWT_REFRESH_SECRET || 'refresh-secret-key',
      });

      
      
      // Tạo access token mới
      const newAccessToken = this.jwtService.sign(
        { _id: payload._id, role: payload.role },
        {
          secret: process.env.JWT_SECRET || '93595f01-2e50-4652-87fe-0601b1cac1c6',
          expiresIn: '15m',
        }
      );
      
      return {
        accessToken: newAccessToken,
      };
    } catch (error) {
      throw new UnauthorizedException('Refresh token không hợp lệ');
    }
  }

  async logout(response: any): Promise<{ message: string }> {
    response.clearCookie('jwt');
    response.clearCookie('refresh_token');
    return { message: 'Đăng xuất thành công' };
  }
}
