import * as jwt from 'jsonwebtoken';
import * as bcrypt from 'bcryptjs';
import { UserService } from '../user/user.service';
import { SignUpDto, SignInDto } from '../user/dto/user.dto';
import { UserRole } from '../user/entities/user.entity';

// Đ<PERSON><PERSON> nghĩa interface cho kiểu trả về
export interface LoginResponse {
  message: string;
  accessToken?: string;
  refreshToken?: string;
  user?: {
    id: string;
    username: string;
    email: string;
    role: UserRole;
  };
}

export interface RefreshTokenResponse {
  accessToken: string;
}

export class AuthService {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  async register(registerDto: SignUpDto): Promise<{ message: string; user?: any }> {
    return this.userService.create(registerDto);
  }

  async login(loginDto: SignInDto, response: any): Promise<LoginResponse> {
    // Tìm user theo email
    const user = await this.userService.findByEmail(loginDto.email);

    if (!user) {
      return { message: "Email không tồn tại!" };
    }

    // Kiểm tra mật khẩu (nên sử dụng bcrypt để hash password)
    const isPasswordValid = await bcrypt.compare(loginDto.password, user.password);
    if (!isPasswordValid) {
      return { message: "Mật khẩu không chính xác!" };
    }

    // Tạo payload cho token
    const payload = {
      id: user.id,
      role: user.role
    };

    // Tạo access token (thời gian ngắn)
    const accessToken = jwt.sign(payload,
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: process.env.JWT_EXPIRATION_TIME || '15m' }
    );

    // Tạo refresh token (thời gian dài)
    const refreshToken = jwt.sign(payload,
      process.env.JWT_REFRESH_SECRET || 'your-refresh-secret',
      { expiresIn: process.env.JWT_REFRESH_EXPIRATION_TIME || '7d' }
    );
    
    // Lưu token vào cookie
    if (response && response.cookie) {
      response.cookie('jwt', accessToken, {
        httpOnly: true,
        maxAge: 15 * 60 * 1000, // 15 phút
      });
      
      response.cookie('refresh_token', refreshToken, {
        httpOnly: true,
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 ngày
      });
    }
    
    return {
      message: 'Đăng nhập thành công',
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      }
    };
  }

  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    try {
      // Xác thực refresh token
      const payload = jwt.verify(refreshToken,
        process.env.JWT_REFRESH_SECRET || 'your-refresh-secret'
      ) as any;

      // Tạo access token mới
      const newAccessToken = jwt.sign(
        { id: payload.id, role: payload.role },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '15m' }
      );

      return {
        accessToken: newAccessToken,
      };
    } catch (error) {
      throw new Error('Refresh token không hợp lệ');
    }
  }

  async logout(response: any): Promise<{ message: string }> {
    response.clearCookie('jwt');
    response.clearCookie('refresh_token');
    return { message: 'Đăng xuất thành công' };
  }
}
