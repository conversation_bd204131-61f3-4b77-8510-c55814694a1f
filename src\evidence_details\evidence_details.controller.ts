import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { EvidenceDetailsService } from './evidence_details.service';
import { CreateEvidenceDetailDto } from './dto/create-evidence_detail.dto';
import { UpdateEvidenceDetailDto } from './dto/update-evidence_detail.dto';

@Controller('evidence-details')
export class EvidenceDetailsController {
  constructor(private readonly evidenceDetailsService: EvidenceDetailsService) {}

  @Post()
  create(@Body() createEvidenceDetailDto: CreateEvidenceDetailDto) {
    return this.evidenceDetailsService.create(createEvidenceDetailDto);
  }

  @Get()
  findAll() {
    return this.evidenceDetailsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.evidenceDetailsService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateEvidenceDetailDto: UpdateEvidenceDetailDto) {
    return this.evidenceDetailsService.update(+id, updateEvidenceDetailDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.evidenceDetailsService.remove(+id);
  }
}
