import { Router } from 'express';
import { body, query, param } from 'express-validator';
import { authMiddleware, roleMiddleware } from '../middleware/auth';
import { UserRole } from '../user/entities/user.entity';
import { validationErrorHandler } from '../middleware/validationErrorHandler';

const router = Router();

// Placeholder routes - implement controllers as needed
router.get('/', 
  authMiddleware,
  roleMiddleware(UserRole.ADMIN, UserRole.USER),
  (req, res) => {
    res.json({ message: 'Evidence Details API - implement controller' });
  }
);

router.post('/',
  authMiddleware,
  roleMiddleware(UserRole.ADMIN),
  (req, res) => {
    res.json({ message: 'Create evidence detail - implement controller' });
  }
);

export default router;
