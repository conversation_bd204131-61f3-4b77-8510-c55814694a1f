import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Logger, Level } from './entities/logger.entity';
import { LoggerDto } from './dto/logger.dto';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class LoggerService {
  constructor(
    @InjectRepository(Logger)
    private readonly loggerRepository: Repository<Logger>,
    private eventEmitter: EventEmitter2
  ) {}

  // Phương thức để tạo log mới
  async create(loggerDto: LoggerDto): Promise<Logger> {
    const newLog = this.loggerRepository.create({
      level: loggerDto.level as Level,
      message: loggerDto.message,
      timestamp: loggerDto.timestamp || new Date()
    });

    return this.loggerRepository.save(newLog);
  }

  // Phương thức để lấy tất cả log
  async findAll(): Promise<Logger[]> {
    return this.loggerRepository.find({
      order: {
        timestamp: 'ASC'
      }
    });
  }

  // Phương thức để lấy log theo level
  async findByLevel(level: Level): Promise<Logger[]> {
    return this.loggerRepository.find({
      where: { level },
      order: {
        timestamp: 'ASC'
      }
    });
  }

  // Phương thức để emit log event
  log(level: Level, message: string): void {
    const logData = {
      level,
      message,
      timestamp: new Date()
    };
    
    this.eventEmitter.emit('log.created', logData);
  }

  // Phương thức tiện ích cho các level khác nhau
  info(message: string): void {
    this.log(Level.info, message);
  }

  warning(message: string): void {
    this.log(Level.warning, message);
  }

  error(message: string): void {
    this.log(Level.error, message);
  }

  // Lắng nghe sự kiện log.created và lưu vào database
  @OnEvent('log.created')
  async handleLogCreatedEvent(payload: LoggerDto) {
    await this.create(payload);
  }
}
