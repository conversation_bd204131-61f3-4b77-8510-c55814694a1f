{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "tsc", "format": "prettier --write \"src/**/*.ts\"", "start": "node dist/main.js", "dev": "ts-node-dev --respawn --transpile-only src/main.ts", "start:debug": "ts-node-dev --inspect --respawn --transpile-only src/main.ts", "start:prod": "node dist/main.js", "lint": "eslint \"src/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5", "cookie-parser": "^1.4.7", "handlebars": "^4.7.8", "multer": "^2.0.1", "nodemailer": "^7.0.4", "pg": "^8.16.0", "typeorm": "^0.3.24", "socket.io": "^4.7.4", "class-transformer": "^0.5.1", "class-validator": "^0.14.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/cookie-parser": "^1.4.6", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.13", "@types/node": "^22.10.7", "@types/jest": "^29.5.14", "@types/supertest": "^6.0.2", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "prettier": "^3.4.2", "jest": "^29.7.0", "ts-jest": "^29.2.5", "supertest": "^7.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}