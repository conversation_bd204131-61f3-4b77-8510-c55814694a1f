import { SignUpDto } from './dto/user.dto';
import { User } from './entities/user.entity';
import { Repository } from 'typeorm';
import { UpdateUserDto } from './dto/user.dto';
import { PaginationDto } from '../people/dto/pagination.dto';
import { DEFAULT_PAGE_SIZE } from '../ultils/constants';
import { AppDataSource } from '../config/database';

export class UserService {
  private userRepository: Repository<User>;

  constructor() {
    this.userRepository = AppDataSource.getRepository(User);
  }

  async create(userDto: SignUpDto) {
    // Check if email already exists
    const emailExists = await this.userRepository.findOne({
      where: { email: userDto.email }
    });

    if (emailExists) {
      throw new Error("Email đã được sử dụng!");
    }

    // Check if username already exists
    const usernameExists = await this.userRepository.findOne({
      where: { username: userDto.username }
    });

    if (usernameExists) {
      throw new Error("Tên đăng nhập đã tồn tại!");
    }

    // Create the entity first, then save it
    const newUser = this.userRepository.create(userDto);
    await this.userRepository.save(newUser);

    console.log(`New user created: ${newUser.username} (email: ${newUser.email})`);

    return { message: 'Đăng ký thành công', user: newUser };
  }

  async findByEmail(email: string) {
    return this.userRepository.findOne({
      where: { email, IsDeleted: false }
    });
  }

  async findAll(paginationDto: PaginationDto) {
    const [data, total] = await this.userRepository.findAndCount({
      skip: paginationDto.skip,
      take: paginationDto.limit ?? DEFAULT_PAGE_SIZE,
      where: { IsDeleted: false }
    })

    return {
      message: 'Users retrieved successfully',
      total: total,
      data
    }
  }

  findOne(type: string, result: string) {
    if (type === 'username') {
      return this.userRepository.findOne({
        where: { username: result }
      });
    } else if (type === 'email') {
      return this.userRepository.findOne({
        where: { email: result }
      });
    } else {
      throw new Error('User not found');
    }
  }

  async update(id: number, updateUserDto: UpdateUserDto) {
    const user = await this.userRepository.findOne({
      where: { id: id.toString() }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Set current date for updatedAt
    updateUserDto.updatedAt = new Date();

    // Update user properties
    Object.assign(user, updateUserDto);

    // Save the updated user
    await this.userRepository.save(user);

    console.log(`User updated: ${user.username} (email: ${user.email})`);

    return { message: 'User updated successfully' };
  }

  async remove(id: number) {
    const user = await this.userRepository.findOne({
      where: { id: id.toString() }
    });

    if (!user) {
      throw new Error('User not found');
    }

    user.IsDeleted = true;

    await this.userRepository.save(user);
    console.log(`User deleted: ${user.username} (email: ${user.email})`);
    return { message: 'User deleted successfully' };
  }


  async searchQuery(searchTerm: string, paginationDto: PaginationDto) {
    // Sử dụng QueryBuilder để có nhiều kiểm soát hơn
    const queryBuilder = this.userRepository.createQueryBuilder('user');

    // Thêm điều kiện tìm kiếm với OR
    queryBuilder.where('user.IsDeleted = false')
      .andWhere(
        '(user.username LIKE :username OR ' +
        'user.email LIKE :email)',
        {
          username: `%${searchTerm}%`,
          email: `%${searchTerm}%`
        }
      );

    // Thêm phân trang
    queryBuilder.skip(paginationDto.skip)
      .take(paginationDto.limit ?? DEFAULT_PAGE_SIZE);

    // Thực hiện truy vấn
    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      message: 'Users retrieved successfully',
      total,
      data
    };
  }
}
