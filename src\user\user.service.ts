import { Injectable, NotFoundException, HttpException, HttpStatus } from '@nestjs/common';
import { SignUpDto } from './dto/user.dto';
import { User } from './entities/user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Repository } from 'typeorm';
import { UpdateUserDto } from './dto/user.dto';
import { PaginationDto } from 'src/people/dto/pagination.dto';
import { DEFAULT_PAGE_SIZE } from 'src/ultils/constants';
import { LoggerService } from 'src/logger/logger.service';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly loggerService: LoggerService
  ) { }

  async create(userDto: SignUpDto) {
    

    // Check if email already exists
    const emailExists = await this.userRepository.findOne({
      where: { email: userDto.email }
    });

    if (emailExists) {
      return { message: "Email đã được sử dụng!" };
    }

    // Check if username already exists
    const usernameExists = await this.userRepository.findOne({
      where: { username: userDto.username }
    });

    if (usernameExists) {
      return { message: "Tên đăng nhập đã tồn tại!" };
    }

    // Create the entity first, then save it
    const newUser = this.userRepository.create(userDto);
    await this.userRepository.save(newUser);

    this.loggerService.info(`New user created: ${newUser.username} (email: ${newUser.email})`);

    return new HttpException('Đăng ký thành công', HttpStatus.CREATED);
  }

  async findByEmail(email: string) {
    return this.userRepository.findOne({
      where: { email, IsDeleted: false }
    });
  }

  async findAll(paginationDto: PaginationDto) {
    const [data, total] = await this.userRepository.findAndCount({
      skip: paginationDto.skip,
      take: paginationDto.limit ?? DEFAULT_PAGE_SIZE,
      where: { IsDeleted: false }
    })

    return {
      message: 'Users retrieved successfully',
      total: total,
      data
    }
  }

  findOne(type: string, result: string) {
    if (type === 'username') {
      return this.userRepository.findOne({
        where: { username: result }
      });
    } else if (type === 'email') {
      return this.userRepository.findOne({
        where: { email: result }
      });
    } else {
      return new NotFoundException('User not found');
    }
  }

  async update(id: number, updateUserDto: UpdateUserDto) {
    const user = await this.userRepository.findOne({
      where: { id: id.toString() }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Set current date for updatedAt
    updateUserDto.updatedAt = new Date();

    // Update user properties
    Object.assign(user, updateUserDto);

    // Save the updated user
    await this.userRepository.save(user);

    this.loggerService.info(`User updated: ${user.username} (email: ${user.email})`);

    return { message: 'User updated successfully' };
  }

  async remove(id: number) {
    const user = await this.userRepository.findOne({
      where: { id: id.toString() }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.IsDeleted = true;

    await this.userRepository.save(user);
    this.loggerService.info(`User deleted: ${user.username} (email: ${user.email})`);
    return { message: 'User deleted successfully' };
  }


  async searchQuery(searchTerm: string, paginationDto: PaginationDto) {
    // Sử dụng QueryBuilder để có nhiều kiểm soát hơn
    const queryBuilder = this.userRepository.createQueryBuilder('user');

    // Thêm điều kiện tìm kiếm với OR
    queryBuilder.where('user.IsDeleted = false')
      .andWhere(
        '(user.username LIKE :username OR ' +
        'user.email LIKE :email)',
        {
          username: `%${searchTerm}%`,
          email: `%${searchTerm}%`
        }
      );

    // Thêm phân trang
    queryBuilder.skip(paginationDto.skip)
      .take(paginationDto.limit ?? DEFAULT_PAGE_SIZE);

    // Thực hiện truy vấn
    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      message: 'Users retrieved successfully',
      total,
      data
    };
  }
}
