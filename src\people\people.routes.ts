import { Router } from 'express';
import { body, query, param } from 'express-validator';
import { PeopleController } from './people.controller';
import { authMiddleware, roleMiddleware } from '../middleware/auth';
import { UserRole } from '../user/entities/user.entity';
import { validationErrorHandler } from '../middleware/validationErrorHandler';

const router = Router();
const peopleController = new PeopleController();

// Validation rules
const createPeopleValidation = [
  body('CCCD').notEmpty().withMessage('CCCD is required'),
  body('fullname').notEmpty().withMessage('Full name is required'),
  body('dateOfBirth').isISO8601().withMessage('Date of birth must be a valid date'),
  body('gender').isIn(['MALE', 'FEMALE']).withMessage('Gender must be MALE or FEMALE'),
  validationErrorHandler
];

const updatePeopleValidation = [
  param('id').notEmpty().withMessage('ID is required'),
  body('fullname').optional().notEmpty().withMessage('Full name cannot be empty'),
  body('dateOfBirth').optional().isISO8601().withMessage('Date of birth must be a valid date'),
  body('gender').optional().isIn(['MALE', 'FEMALE']).withMessage('Gender must be MALE or FEMALE'),
  validationErrorHandler
];

const paginationValidation = [
  query('skip').optional().isNumeric().withMessage('Skip must be a number'),
  query('limit').optional().isNumeric().withMessage('Limit must be a number'),
  validationErrorHandler
];

const searchValidation = [
  query('searchTerm').notEmpty().withMessage('Search term is required'),
  ...paginationValidation
];

// Routes
router.post('/create-people',
  authMiddleware,
  createPeopleValidation,
  peopleController.create.bind(peopleController)
);

router.get('/get-all-people',
  authMiddleware,
  roleMiddleware(UserRole.ADMIN, UserRole.USER),
  paginationValidation,
  peopleController.findAll.bind(peopleController)
);

router.get('/get-people/:id',
  param('id').notEmpty().withMessage('ID is required'),
  validationErrorHandler,
  peopleController.findOne.bind(peopleController)
);

router.get('/search-people',
  searchValidation,
  peopleController.search.bind(peopleController)
);

router.put('/update-people/:id',
  updatePeopleValidation,
  peopleController.update.bind(peopleController)
);

router.put('/delete-people/:id',
  param('id').notEmpty().withMessage('ID is required'),
  validationErrorHandler,
  peopleController.remove.bind(peopleController)
);

router.get('/filter-people/:type/:result',
  param('type').isIn(['CCCD', 'fullname']).withMessage('Type must be CCCD or fullname'),
  param('result').notEmpty().withMessage('Result is required'),
  validationErrorHandler,
  peopleController.filter.bind(peopleController)
);

export default router;
