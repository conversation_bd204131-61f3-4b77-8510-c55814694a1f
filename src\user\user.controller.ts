import { Request, Response } from 'express';
import { UserService } from './user.service';
import { SignUpDto, UpdateUserDto } from './dto/user.dto';
import { PaginationDto } from '../people/dto/pagination.dto';

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  async findAll(req: Request, res: Response) {
    try {
      const { skip = 0, limit = 10 } = req.query;
      const result = await this.userService.findAll({
        skip: Number(skip),
        limit: Number(limit)
      });
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  }

  async create(req: Request, res: Response) {
    try {
      const result = await this.userService.create(req.body);
      res.status(201).json(result);
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Bad request'
      });
    }
  }

  async update(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const result = await this.userService.update(id, req.body);
      res.status(200).json(result);
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Bad request'
      });
    }
  }

  async remove(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      const result = await this.userService.remove(id);
      res.status(200).json(result);
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Bad request'
      });
    }
  }

  async search(req: Request, res: Response) {
    try {
      const { searchTerm, skip = 0, limit = 10 } = req.query;
      const result = await this.userService.searchQuery(searchTerm as string, {
        skip: Number(skip),
        limit: Number(limit)
      });
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  }
}
