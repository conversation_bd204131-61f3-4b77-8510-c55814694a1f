import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Put, Query } from '@nestjs/common';
import { UserService } from './user.service';
import { SignUpDto, UpdateUserDto } from './dto/user.dto';
import { AuthGuard } from 'src/auth/guards/auth.guard';
import { RoleGuard } from 'src/auth/guards/role.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from './entities/user.entity';
import { PaginationDto } from 'src/people/dto/pagination.dto';

@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @UseGuards(AuthGuard, RoleGuard)
  @Roles(UserRole.ADMIN, UserRole.USER)
  @Get('get-all-user')
  findAll(@Query() paginationDto: PaginationDto) {
    return this.userService.findAll({
      skip: paginationDto.skip,
      limit: paginationDto.limit
    });
  }

  @UseGuards(AuthGuard, RoleGuard)
  @Roles(UserRole.ADMIN, UserRole.USER)
  @Post('create')
  create(@Body() createUserDto: SignUpDto) {
    return this.userService.create(createUserDto);
  }
  
  @UseGuards(AuthGuard, RoleGuard)
  @Roles(UserRole.ADMIN)
  @Put('update-user/:id')
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.userService.update(+id, updateUserDto);
  }

  @UseGuards(AuthGuard, RoleGuard)
  @Roles(UserRole.ADMIN)
  @Delete('delete-user/:id')
  remove(@Param('id') id: string) {
    return this.userService.remove(+id);
  }

  @UseGuards(AuthGuard, RoleGuard)
  @Roles(UserRole.ADMIN, UserRole.USER)
  @Get('search-user')
  search(@Query('searchTerm') searchTerm: string, @Query() paginationDto: PaginationDto) {
    return this.userService.searchQuery(searchTerm, {
      skip: paginationDto.skip,
      limit: paginationDto.limit
    });
  }
}
