import { Modu<PERSON> } from '@nestjs/common';
import { CriminalRecordsService } from './criminal_records.service';
import { CriminalRecordsController } from './criminal_records.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CriminalRecord } from './entities/criminal_record.entity';
import { People } from 'src/people/entities/people.entity';
import { LoggerModule } from 'src/logger/logger.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CriminalRecord, People]),
  LoggerModule
],
  controllers: [CriminalRecordsController],
  providers: [CriminalRecordsService],  
})
export class CriminalRecordsModule {}
