import { Injectable } from '@nestjs/common';
import { CreateEvidenceDetailDto } from './dto/create-evidence_detail.dto';
import { UpdateEvidenceDetailDto } from './dto/update-evidence_detail.dto';

@Injectable()
export class EvidenceDetailsService {
  create(createEvidenceDetailDto: CreateEvidenceDetailDto) {
    return 'This action adds a new evidenceDetail';
  }

  findAll() {
    return `This action returns all evidenceDetails`;
  }

  findOne(id: number) {
    return `This action returns a #${id} evidenceDetail`;
  }

  update(id: number, updateEvidenceDetailDto: UpdateEvidenceDetailDto) {
    return `This action updates a #${id} evidenceDetail`;
  }

  remove(id: number) {
    return `This action removes a #${id} evidenceDetail`;
  }
}
