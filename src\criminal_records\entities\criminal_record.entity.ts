import { EvidenceDetail } from 'src/evidence_details/entities/evidence_detail.entity';
import { People } from 'src/people/entities/people.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

export enum status {
    PROGRESS = 'PROGRESS',
    COMPLETED = 'COMPLETED',
}

@Entity()
export class CriminalRecord {
    @PrimaryGeneratedColumn()
    id: string;

    @Column({
        nullable: false,
    })
    offense: string;

    @Column({
        nullable: false,
    })
    date: Date;

    @Column({
        nullable: false,
    })
    location: string;

    @Column({
        nullable: false,
    })
    sentence: string;

    @Column({
        nullable: false,
        default: status.PROGRESS,
        type: 'enum',
        enum: status,
    })
    status: status;

    @Column({
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @Column({
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @Column({
        default: false,
    })
    IsDeleted: boolean;

    @ManyToOne(() => People, (people) => people.criminalRecords, {
      nullable: false, // Thêm ràng buộc NOT NULL
    })
    @JoinColumn({ name: 'CCCD' }) // Đặt tên cột trong database
    people: People;

    @OneToMany(() => EvidenceDetail, (evidenceDetail) => evidenceDetail.criminalRecord)
    evidenceDetails: EvidenceDetail[];
}
