
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    // console.log('Cookies:', request.cookies);
    
    // Try cookie token first
    let cookieToken = request.cookies && request.cookies['jwt'];
    let headerToken = this.extractTokenFromHeader(request);
    
    // console.log('Token from cookie:', cookieToken);
    // console.log('Token from header:', headerToken);
    
    // Try to verify cookie token
    if (cookieToken) {
      try {
        const payload = await this.jwtService.verifyAsync(
          cookieToken,
          {
            secret: process.env.JWT_SECRET || '93595f01-2e50-4652-87fe-0601b1cac1c6',
          }
        );
        // console.log('Cookie token verified successfully, payload:', payload);
        request['user'] = payload;
        return true;
      } catch (error) {
        // console.error('Cookie token verification failed:', error.message);
        // Continue to try header token
      }
    }
    
    // Try to verify header token
    if (headerToken) {
      try {
        const payload = await this.jwtService.verifyAsync(
          headerToken,
          {
            secret: process.env.JWT_SECRET || '93595f01-2e50-4652-87fe-0601b1cac1c6',
          }
        );
        // console.log('Header token verified successfully, payload:', payload);
        request['user'] = payload;
        return true;
      } catch (error) {
        // console.error('Header token verification failed:', error.message);
      }
    }
    
    // If we get here, both tokens failed or were not present
    throw new UnauthorizedException();
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
