import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';

export const validationErrorHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      property: error.type === 'field' ? (error as any).path : 'unknown',
      constraints: { [error.type]: error.msg },
      value: error.type === 'field' ? (error as any).value : undefined
    }));

    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: formattedErrors
    });
  }
  
  next();
};
