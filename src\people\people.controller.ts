import { Controller, Get, Post, Body, Patch, Param, Delete, Req, Put, UseGuards, Query, UseInterceptors, UploadedFile } from '@nestjs/common';
import { PeopleService } from './people.service';
import { CreatePeopleDto, UpdatePeopleDto } from './dto/people.dto';
import { Request } from 'express';
import { AuthGuard } from 'src/auth/guards/auth.guard';
import { PaginationDto } from './dto/pagination.dto';
import { RoleGuard } from 'src/auth/guards/role.guard';
import { UserRole } from 'src/user/entities/user.entity';
import { Roles } from 'src/auth/decorators/roles.decorator';


@Controller('people')
export class PeopleController {
  constructor(private readonly peopleService: PeopleService) { }

  @UseGuards(AuthGuard)
  @Post('create-people')
  create(@Body() createPeopleDto: CreatePeopleDto, @Req() request: Request) {
    const user = request['user'];

    // Lấy _id từ thông tin người dùng
    const userId = user?._id;

    if (!userId) {
      throw new Error('User not found');
    }

    return this.peopleService.create(createPeopleDto, userId);
  }

  @UseGuards(AuthGuard, RoleGuard)
  @Roles(UserRole.ADMIN, UserRole.USER)
  @Get('get-all-people')
  findAll(@Query() paginationDto: PaginationDto) {
    return this.peopleService.findAll({
      skip: paginationDto.skip,
      limit: paginationDto.limit
    });
  }

  @Get('get-people/:id')
  findOne(@Param('id') id: string) {
    return this.peopleService.findOne(+id);
  }


  @Get('search-people')
  search(@Query('searchTerm') searchTerm: string, @Query() paginationDto: PaginationDto) {
    return this.peopleService.searchQuery(searchTerm, {
      skip: paginationDto.skip,
      limit: paginationDto.limit
    });
  }


  @Put('update-people/:id')
  update(@Param('id') id: string, @Body() updatePeopleDto: UpdatePeopleDto) {
    return this.peopleService.update(id, updatePeopleDto);
  }

  @Put('delete-people/:id')
  remove(@Param('id') id: string) {
    return this.peopleService.remove(id);
  }

  @Get('filter-people/:type/:result')
  filter(@Param('type') type: string, @Param('result') result: string) {
    return this.peopleService.filter(type, result);
  }

}
