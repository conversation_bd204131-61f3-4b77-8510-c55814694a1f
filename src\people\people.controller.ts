import { Request, Response } from 'express';
import { PeopleService } from './people.service';
import { CreatePeopleDto, UpdatePeopleDto } from './dto/people.dto';
import { AuthRequest } from '../middleware/auth';

export class PeopleController {
  private peopleService: PeopleService;

  constructor() {
    this.peopleService = new PeopleService();
  }

  async create(req: AuthRequest, res: Response) {
    try {
      const user = req.user;
      const userId = user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'User not found'
        });
      }

      const result = await this.peopleService.create(req.body, userId);
      res.status(201).json(result);
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Bad request'
      });
    }
  }

  async findAll(req: Request, res: Response) {
    try {
      const { skip = 0, limit = 10 } = req.query;
      const result = await this.peopleService.findAll({
        skip: Number(skip),
        limit: Number(limit)
      });
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  }

  async findOne(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const result = await this.peopleService.findOne(id);
      res.status(200).json(result);
    } catch (error) {
      res.status(404).json({
        success: false,
        message: error instanceof Error ? error.message : 'Person not found'
      });
    }
  }

  async search(req: Request, res: Response) {
    try {
      const { searchTerm, skip = 0, limit = 10 } = req.query;
      const result = await this.peopleService.searchQuery(searchTerm as string, {
        skip: Number(skip),
        limit: Number(limit)
      });
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  }


  async update(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const result = await this.peopleService.update(id, req.body);
      res.status(200).json(result);
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Bad request'
      });
    }
  }

  async remove(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const result = await this.peopleService.remove(id);
      res.status(200).json(result);
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Bad request'
      });
    }
  }

  async filter(req: Request, res: Response) {
    try {
      const { type, result } = req.params;
      const data = await this.peopleService.filter(type, result);
      res.status(200).json({
        success: true,
        data
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Bad request'
      });
    }
  }
}
