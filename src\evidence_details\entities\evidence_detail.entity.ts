
import { v4 as uuidv4 } from 'uuid';
import { CriminalRecord } from 'src/criminal_records/entities/criminal_record.entity';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class EvidenceDetail {
    @PrimaryGeneratedColumn()
    id: string;

    @Column({
        nullable: false,
    })
    criminalRecordId: string;

    @Column({
        nullable: false,
    })
    image_url : string;

    @Column({
        nullable: false,
        type: 'text',
        default: '...',
    })
    description: string;

    @Column({
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @Column({
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @ManyToOne(() => CriminalRecord, (criminalRecord) => criminalRecord.evidenceDetails)
    criminalRecord: CriminalRecord;
}
