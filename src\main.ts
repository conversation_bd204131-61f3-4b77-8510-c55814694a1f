import { NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './app.module';
import { ClassSerializerInterceptor, ValidationPipe, BadRequestException } from '@nestjs/common';
import * as cookieParse from 'cookie-parser';


async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.setGlobalPrefix('api/v1');
  
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    transformOptions: {
      enableImplicitConversion: true,
    },
    exceptionFactory: (errors) => {
      console.error('Validation errors:', JSON.stringify(errors));
      const messages = errors.map(error => ({
        property: error.property,
        constraints: error.constraints,
        value: error.value
      }));
      return new BadRequestException(messages);
    }
  }));
  
  app.use(cookieParse());
  app.enableCors({
    origin: [
      `http://localhost:5173`,
      `http://localhost:5174`,
      `http://localhost:3000`,
      `http://*************:3000`
    ],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));
  
  await app.listen(process.env.PORT ?? 3000);
  console.log(`Application is running on: http://localhost:${process.env.PORT ?? 3000}`);
}
bootstrap();
