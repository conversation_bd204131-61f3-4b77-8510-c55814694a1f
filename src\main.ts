import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import * as dotenv from 'dotenv';
import * as cookieParser from 'cookie-parser';
import * as path from 'path';
import rateLimit from 'express-rate-limit';
import { initializeDatabase } from './config/database';
import { errorHandler } from './middleware/errorHandler';
import { validationErrorHandler } from './middleware/validationErrorHandler';

// Import routes
import userRoutes from './user/user.routes';
import authRoutes from './auth/auth.routes';
import peopleRoutes from './people/people.routes';
import criminalRecordsRoutes from './criminal_records/criminal_records.routes';
import evidenceDetailsRoutes from './evidence_details/evidence_details.routes';
import uploadRoutes from './upload/upload.routes';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Middleware
app.use(helmet());
app.use(limiter);
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3000',
    'http://*************:3000'
  ],
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
  credentials: true,
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Static files
app.use('/uploads', express.static(path.join(__dirname, '..', 'uploads')));
app.use(express.static(path.join(__dirname, '..', 'client/dist')));

// API routes
app.use('/api/v1/user', userRoutes);
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/people', peopleRoutes);
app.use('/api/v1/criminal-records', criminalRecordsRoutes);
app.use('/api/v1/evidence-details', evidenceDetailsRoutes);
app.use('/api/v1/upload', uploadRoutes);

// Validation error handler
app.use(validationErrorHandler);

// Global error handler
app.use(errorHandler);

// Serve frontend for any non-API routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '..', 'client/dist', 'index.html'));
});

async function bootstrap() {
  try {
    // Initialize database
    await initializeDatabase();

    // Start server
    app.listen(PORT, () => {
      console.log(`Application is running on: http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('Failed to start application:', error);
    process.exit(1);
  }
}

bootstrap();
