import { IsNotEmpty, IsDate } from "class-validator";

enum gender {
    MALE = 'MALE',
    FEMALE = 'FEMALE',
}

enum MariageStatus{
    MARRIED = 'MARRIED',
    SINGLE = 'SINGLE',
}


export class PeopleDto {
    @IsNotEmpty()
    CCCD: string;
    @IsNotEmpty()
    fullname: string;
    @IsNotEmpty()
    gender: gender;
    @IsNotEmpty()
    address: string;

    @IsNotEmpty()
    @IsDate()
    dateOfbirth: Date;

    avatar: string;

    phone: string;
    MariageStatus: MariageStatus;
}

export class CreatePeopleDto extends PeopleDto {}

export class UpdatePeopleDto {
    @IsNotEmpty()
    fullname: string;
    @IsNotEmpty()
    gender: gender;
    @IsNotEmpty()
    address: string;

    @IsNotEmpty()
    @IsDate()
    dateOfbirth: Date;

    updatedAt: Date;

    avatar: string;
    phone: string;
    MariageStatus: MariageStatus;
}

