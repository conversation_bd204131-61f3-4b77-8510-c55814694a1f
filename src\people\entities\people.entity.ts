
import { v4 as uuidv4 } from 'uuid';
import { CriminalRecord } from 'src/criminal_records/entities/criminal_record.entity';
import { Column, Entity, OneToMany, PrimaryColumn, PrimaryGeneratedColumn } from 'typeorm';

enum gender {
    MALE = 'MALE',
    FEMALE = 'FEMALE',
}

enum MariageStatus{
    MARRIED = 'MARRIED',
    SINGLE = 'SINGLE',
}

@Entity()
export class People {
    @PrimaryColumn(
        {
            nullable: false,
            unique: true,
        }
    )
    CCCD: string;

    @Column({
        nullable: true,
        default: '/uploads/images/1751899622745-834467477.jpg',
    })
    avatar : string;

    @Column({
        nullable: false,
    })
    fullname: string;

    @Column({
        nullable: false,
    })
    dateOfbirth: Date;

    @Column({
        nullable: false,
    })
    gender: gender;

    @Column({
        nullable: false,
    })
    address: string;

    @Column({
        nullable: true,
        unique: true,
    })
    phone: string;

    @Column({
        nullable: true,
        default: MariageStatus.SINGLE,
    })
    MariageStatus: MariageStatus;

    @Column({
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @Column({
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @OneToMany(() => CriminalRecord, (criminalRecord) => criminalRecord.people)
    criminalRecords: CriminalRecord[];

    @Column({
        default: false,
    })
    IsDeleted: boolean;
}
