import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { LoggerService } from './logger.service';
import { LoggerDto } from './dto/logger.dto';
import { Level } from './entities/logger.entity';

@Controller('logger')
export class LoggerController {
  constructor(private readonly loggerService: LoggerService) {}

  @Post()
  create(@Body() loggerDto: LoggerDto) {
    return this.loggerService.create(loggerDto);
  }

  @Get()
  findAll(@Query('level') level?: Level) {
    if (level) {
      return this.loggerService.findByLevel(level);
    }
    return this.loggerService.findAll();
  }

  @Post('log')
  log(@Body() data: { level: Level, message: string }) {
    this.loggerService.log(data.level, data.message);
    return { success: true, message: 'Log event emitted' };
  }

  @Post('info')
  info(@Body() data: { message: string }) {
    this.loggerService.info(data.message);
    return { success: true, message: 'Info log event emitted' };
  }

  @Post('warning')
  warning(@Body() data: { message: string }) {
    this.loggerService.warning(data.message);
    return { success: true, message: 'Warning log event emitted' };
  }

  @Post('error')
  error(@Body() data: { message: string }) {
    this.loggerService.error(data.message);
    return { success: true, message: 'Error log event emitted' };
  }
}
