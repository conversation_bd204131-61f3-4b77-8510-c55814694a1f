
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { Reflector } from '@nestjs/core';
import { UserRole } from 'src/user/entities/user.entity';

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private reflector: Reflector
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // // Lấy các vai trò được yêu cầu từ decorator
    // const requiredRoles = this.reflector.get<UserRole[]>('roles', context.getHandler());
    
    // // Nếu không có vai trò nào được yêu cầu, cho phép truy cập
    // if (!requiredRoles || requiredRoles.length === 0) {
    //   return true;
    // }
    
    // const request = context.switchToHttp().getRequest();
    
    // // Try cookie token first, then header token
    // let token = null;
    
    // // Check for cookie token
    // if (request.cookies && request.cookies['jwt']) {
    //   token = request.cookies['jwt'];
    // } 
    
    // if (!token) {
    //   console.log('No token found');
    //   throw new UnauthorizedException();
    // }
    
    // try {
    //   const payload = await this.jwtService.verifyAsync(
    //     token,
    //     {
    //       secret: process.env.JWT_SECRET || '93595f01-2e50-4652-87fe-0601b1cac1c6',
    //     }
    //   );
      
    //   // Kiểm tra payload có hợp lệ không
    //   if (!payload) {
    //     throw new UnauthorizedException();
    //   }
      
    //   // Gán payload vào request
    //   request['user'] = payload;
      
    //   // Kiểm tra vai trò của người dùng
    //   const userRole = payload.role as UserRole;
      
    //   // Kiểm tra xem người dùng có vai trò được yêu cầu không
    //   const hasRequiredRole = requiredRoles.includes(userRole);

    //   if (!hasRequiredRole) {
    //     throw new ForbiddenException('You do not have permission to access this resource');
    //   }
    
    //   return true;
    // } catch (error) {
    //   console.error('Role guard error:', error);
    //   if (error instanceof ForbiddenException) {
    //     throw error;
    //   }
    //   throw new UnauthorizedException();
    // }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
