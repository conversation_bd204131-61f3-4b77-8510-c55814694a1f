import { IsNotEmpty, IsEnum, IsOptional } from "class-validator";
import { status } from "../entities/criminal_record.entity";

export class CriminalRecordsDto {
    @IsNotEmpty()
    offense: string;
    
    @IsNotEmpty()
    date: Date;
    
    @IsNotEmpty()
    location: string;
    
    @IsOptional()
    sentence?: string;
    
    @IsOptional()
    @IsEnum(status)
    status?: status;
    
    @IsNotEmpty({ message: 'peopleId is required' })
    peopleId: string;
}

export class UpdateCriminalRecordDto {
    @IsOptional()
    offense: string;
    
    @IsOptional()
    date: Date;
    
    @IsOptional()
    location: string;
    
    @IsOptional()
    sentence: string;
    
    @IsOptional()
    @IsEnum(status)
    status: status;
}

export class createCriminalRecordDto extends CriminalRecordsDto {}
