
import { v4 as uuidv4 } from 'uuid';
import { CriminalRecord } from 'src/criminal_records/entities/criminal_record.entity';
import { Column, Entity, OneToMany, PrimaryColumn, PrimaryGeneratedColumn } from 'typeorm';

export enum Level {
    info = 'info',
    warning = 'warning',
    error = 'error'
}


@Entity()
export class Logger {
    
    @PrimaryGeneratedColumn()
    id: string;

    @Column({
        nullable: false,
    })
    level: Level;

    @Column({
        nullable: false,
    })
    message: string;

    @Column({
        nullable: false,
    })
    timestamp: Date;
}
