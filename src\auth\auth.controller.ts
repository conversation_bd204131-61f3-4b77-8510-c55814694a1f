import { Request, Response } from 'express';
import { AuthService, LoginResponse } from './auth.service';
import { SignUpDto, SignInDto } from '../user/dto/user.dto';
import { AuthRequest } from '../middleware/auth';

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  async me(req: AuthRequest, res: Response) {
    try {
      res.status(200).json({
        success: true,
        user: req.user
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  }

  async register(req: Request, res: Response) {
    try {
      const result = await this.authService.register(req.body);
      res.status(201).json(result);
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Bad request'
      });
    }
  }

  async login(req: Request, res: Response) {
    try {
      const result = await this.authService.login(req.body, res);
      res.status(200).json(result);
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Bad request'
      });
    }
  }

  async refreshToken(req: Request, res: Response) {
    try {
      const { refreshToken } = req.body;
      const result = await this.authService.refreshToken(refreshToken);

      // Cập nhật access token mới vào cookie
      res.cookie('jwt', result.accessToken, {
        httpOnly: true,
        maxAge: 15 * 60 * 1000, // 15 minutes
      });

      res.status(200).json({
        message: 'Token đã được làm mới',
        accessToken: result.accessToken
      });
    } catch (error) {
      res.status(401).json({
        success: false,
        message: 'Refresh token không hợp lệ'
      });
    }
  }

  async logout(req: Request, res: Response) {
    try {
      const result = await this.authService.logout(res);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  }

  async getProfile(req: AuthRequest, res: Response) {
    try {
      res.status(200).json({
        success: true,
        user: req.user
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  }
}
