import { Controller, Post, Body, Res, Req, UnauthorizedException, Get } from '@nestjs/common';
import { AuthService, LoginResponse } from './auth.service';
import { SignUpDto, SignInDto } from 'src/user/dto/user.dto';
import { Request, Response } from 'express';
import { AuthGuard } from './guards/auth.guard';
import { UseGuards } from '@nestjs/common';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @UseGuards(AuthGuard)
  @Get('me')
  me(@Req() request: Request) {
    return request['user'];
  }

  @Post('register')
  register(@Body() registerDto: SignUpDto) {
    return this.authService.register(registerDto);
  }

  @Post('login')
  login(@Body() loginDto: SignInDto, @Res({ passthrough: true }) response: Response): Promise<LoginResponse> {
    return this.authService.login(loginDto, response);
  }
  
  @Post('refresh-token')
  async refreshToken(@Body() body: { refreshToken: string }, @Res({ passthrough: true }) response: Response) {
    try {
    
      const result = await this.authService.refreshToken(body.refreshToken);
      // Cập nhật access token mới vào cookie
      response.cookie('jwt', result.accessToken, {
        httpOnly: true,
        maxAge: 0.1 * 60 * 1000,
      });
      
      return { message: 'Token đã được làm mới', accessToken: result.accessToken };
    } catch (error) {
      throw new UnauthorizedException('Refresh token không hợp lệ');
    }
  }
  
  @Post('logout')
  logout(@Res({ passthrough: true }) response: Response) {
    return this.authService.logout(response);
  }
  
  @Get('profile')
  getProfile(@Req() request: Request) {
    return request['user'];
  }
}
